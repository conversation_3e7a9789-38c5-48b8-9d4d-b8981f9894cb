# Copyright the Hyperledger Fabric contributors. All rights reserved.
#
# SPDX-License-Identifier: Apache-2.0

name: "Broken Link Checker"

on:
  workflow_dispatch:
  schedule:
    - cron: "50 1 * * *"
  pull_request:
    paths:
      - .github/workflows/broken-link-checker.yml

permissions:
  contents: read

jobs:
  broken-lint-checker:
    # Only run the scheduled job in hyperledger/fabric repository, not on personal forks
    if: github.event_name != 'schedule' || (github.event_name == 'schedule' && github.repository == 'hyperledger/fabric')
    name: "Check for Broken Links"
    runs-on: ${{ github.repository == 'hyperledger/fabric' && 'fabric-ubuntu-22.04' || 'ubuntu-22.04' }}
    steps:
      - name: Checkout Fabric Code
        uses: actions/checkout@v4
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version-file: go.mod
      - name: Install Muffet
        run: go install github.com/raviqqe/muffet/v2@latest
      - name: Check Broken Links with Muffet
        # Exclude any links that direct to the documentation or release notes of the non-latest version to limit the scanning to target to that of the latest version.
        run: muffet --max-response-body-size=100000000 --rate-limit=10 --timeout=20 --buffer-size=2147483647 --color=always --exclude="^(https:\/\/hyperledger-fabric.readthedocs.io\/[A-z_]+\/(v[\d]+.[\d]+.[\d]+|release)).*$" https://hyperledger-fabric.readthedocs.io/en/latest/
