# DATAGERRY REST API Documentation

## Overview

DATAGERRY provides a comprehensive REST API for managing CMDB objects, types, categories, and other entities. The API follows RESTful conventions and supports JSON data exchange with authentication and authorization controls.

## Base URL Structure

```
http://your-datagerry-instance/rest/
```

## Authentication

### Authentication Methods

DATAGERRY supports multiple authentication methods:

1. **Bearer Token Authentication** (Recommended)
2. **Basic Authentication** 
3. **API Key Authentication** (Cloud mode)

### Getting an Authentication Token

**Endpoint:** `POST /auth/login`

**Request:**
```json
{
  "user_name": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_issued_at": 1640995200,
  "token_expire": 1641081600,
  "user": {
    "public_id": 1,
    "user_name": "admin",
    "active": true,
    "group_id": 1
  },
  "response_type": "LOGIN",
  "time": "2022-01-01T00:00:00.000000"
}
```

### Required Headers

For all authenticated requests, include:

```http
Authorization: Bearer <your_token>
Content-Type: application/json
Accept: application/json
```

## Core API Endpoints

### Objects API

#### Get All Objects
- **URL:** `GET /objects/`
- **Description:** Retrieve multiple CMDB objects with pagination and filtering
- **Parameters:** [CollectionParameters](#collection-parameters)

**Example Request:**
```http
GET /rest/objects/?limit=10&page=1&sort=public_id&order=1&filter={"type_id": 1}
```

**Example Response:**
```json
{
  "results": [
    {
      "public_id": 1,
      "type_id": 1,
      "version": "1.0.0",
      "author_id": 1,
      "creation_time": {"$date": 1640995200000},
      "last_edit_time": null,
      "active": true,
      "fields": [
        {
          "name": "hostname",
          "value": "server01.example.com"
        }
      ],
      "multi_data_sections": []
    }
  ],
  "count": 1,
  "total": 1,
  "pager": {
    "page": 1,
    "page_size": 10,
    "total_pages": 1
  },
  "response_type": "GET",
  "model": "Object"
}
```

#### Get Single Object
- **URL:** `GET /objects/{public_id}`
- **Description:** Retrieve a specific CMDB object by ID

**Example Request:**
```http
GET /rest/objects/1
```

#### Create Object
- **URL:** `POST /objects/`
- **Description:** Create a new CMDB object
- **Required Rights:** `base.framework.object.add`

**Example Request:**
```json
{
  "type_id": 1,
  "version": "1.0.0",
  "author_id": 1,
  "active": true,
  "fields": [
    {
      "name": "hostname",
      "value": "server01.example.com"
    }
  ]
}
```

#### Update Object
- **URL:** `PUT /objects/{public_id}` or `PATCH /objects/{public_id}`
- **Description:** Update an existing CMDB object
- **Required Rights:** `base.framework.object.edit`

#### Delete Object
- **URL:** `DELETE /objects/{public_id}`
- **Description:** Delete a CMDB object
- **Required Rights:** `base.framework.object.delete`

### Types API

#### Get All Types
- **URL:** `GET /types/`
- **Description:** Retrieve multiple CMDB types
- **Parameters:** [CollectionParameters](#collection-parameters)

#### Get Single Type
- **URL:** `GET /types/{public_id}`
- **Description:** Retrieve a specific CMDB type by ID

#### Create Type
- **URL:** `POST /types/`
- **Description:** Create a new CMDB type
- **Required Rights:** `base.framework.type.add`

**Example Request:**
```json
{
  "name": "server-type",
  "label": "Server Type",
  "author_id": 1,
  "active": true,
  "fields": [
    {
      "type": "text",
      "name": "hostname",
      "label": "Hostname"
    }
  ],
  "render_meta": {
    "icon": "fas fa-server",
    "sections": []
  }
}
```

#### Update Type
- **URL:** `PUT /types/{public_id}` or `PATCH /types/{public_id}`
- **Description:** Update an existing CMDB type
- **Required Rights:** `base.framework.type.edit`

#### Delete Type
- **URL:** `DELETE /types/{public_id}`
- **Description:** Delete a CMDB type
- **Required Rights:** `base.framework.type.delete`

### Categories API

#### Get All Categories
- **URL:** `GET /categories/`
- **Description:** Retrieve multiple CMDB categories
- **Parameters:** [CollectionParameters](#collection-parameters)

**Special Views:**
- `?view=tree` - Returns categories in tree structure
- `?view=list` - Returns categories as flat list (default)

#### Get Single Category
- **URL:** `GET /categories/{public_id}`
- **Description:** Retrieve a specific CMDB category by ID

#### Create Category
- **URL:** `POST /categories/`
- **Description:** Create a new CMDB category
- **Required Rights:** `base.framework.category.add`

#### Update Category
- **URL:** `PUT /categories/{public_id}` or `PATCH /categories/{public_id}`
- **Description:** Update an existing CMDB category
- **Required Rights:** `base.framework.category.edit`

#### Delete Category
- **URL:** `DELETE /categories/{public_id}`
- **Description:** Delete a CMDB category
- **Required Rights:** `base.framework.category.delete`

## Data Structures

### CmdbObject Schema

```json
{
  "public_id": 1,
  "type_id": 1,
  "version": "1.0.0",
  "author_id": 1,
  "editor_id": null,
  "creation_time": {"$date": 1640995200000},
  "last_edit_time": null,
  "active": true,
  "fields": [
    {
      "name": "field-name",
      "value": "field-value"
    }
  ],
  "multi_data_sections": []
}
```

**Field Descriptions:**
- `public_id` (integer): Unique identifier for the object
- `type_id` (integer): Reference to the CmdbType this object is based on
- `version` (string): Version of the object (default: "1.0.0")
- `author_id` (integer): ID of the user who created the object
- `editor_id` (integer, nullable): ID of the user who last edited the object
- `creation_time` (object): Timestamp when the object was created
- `last_edit_time` (object, nullable): Timestamp when the object was last edited
- `active` (boolean): Whether the object is active (default: true)
- `fields` (array): Array of field objects containing name-value pairs
- `multi_data_sections` (array): Array of multi-data section entries

### CmdbType Schema

```json
{
  "public_id": 1,
  "name": "server-type",
  "label": "Server Type",
  "author_id": 1,
  "editor_id": null,
  "creation_time": {"$date": 1640995200000},
  "last_edit_time": null,
  "active": true,
  "selectable_as_parent": true,
  "global_template_ids": [],
  "fields": [
    {
      "type": "text",
      "name": "hostname",
      "label": "Hostname",
      "required": true
    }
  ],
  "version": "1.0.0",
  "description": "Type for server objects",
  "render_meta": {
    "icon": "fas fa-server",
    "sections": []
  }
}
```

### CmdbCategory Schema

```json
{
  "public_id": 1,
  "name": "infrastructure",
  "label": "Infrastructure",
  "parent": null,
  "types": [1, 2, 3],
  "meta": {
    "icon": "fas fa-network-wired",
    "order": 1
  }
}
```

## Collection Parameters

All collection endpoints support the following query parameters for pagination, filtering, and sorting:

### Parameters

- `limit` (integer, default: 10): Maximum number of results to return (0 = unlimited)
- `page` (integer, default: 1): Page number for pagination
- `sort` (string, default: "public_id"): Field to sort by
- `order` (integer, default: 1): Sort order (1 = ascending, -1 = descending)
- `filter` (object): MongoDB-style filter criteria
- `projection` (object, optional): Fields to include/exclude in results

### Example Usage

```http
GET /rest/objects/?limit=20&page=2&sort=creation_time&order=-1&filter={"active":true,"type_id":1}
```

### Filter Examples

**Simple filter:**
```json
{"type_id": 1}
```

**Complex filter with multiple conditions:**
```json
{
  "active": true,
  "type_id": {"$in": [1, 2, 3]},
  "creation_time": {"$gte": {"$date": 1640995200000}}
}
```

**Aggregation pipeline filter:**
```json
[
  {"$match": {"active": true}},
  {"$match": {"type_id": {"$in": [1, 2, 3]}}}
]
```

## Response Structure

### Standard Response Format

All API responses follow a consistent structure:

```json
{
  "response_type": "GET|POST|PUT|DELETE",
  "model": "Object|Type|Category",
  "time": "2022-01-01T00:00:00.000000",
  "result": {},  // For single item responses
  "results": [], // For collection responses
  "count": 10,   // Number of items in current page
  "total": 100,  // Total number of items
  "pager": {
    "page": 1,
    "page_size": 10,
    "total_pages": 10
  },
  "pagination": {
    "current": "http://example.com/rest/objects/?page=1",
    "first": "http://example.com/rest/objects/?page=1",
    "prev": null,
    "next": "http://example.com/rest/objects/?page=2",
    "last": "http://example.com/rest/objects/?page=10"
  }
}
```

### Error Response Format

```json
{
  "status": 400,
  "prefix": "Bad Request",
  "description": "The request could not be understood by the server",
  "message": "Specific error message",
  "joke": "... cause the access was nuts!"
}
```

## HTTP Status Codes

- `200 OK`: Successful GET, PUT, PATCH requests
- `201 Created`: Successful POST requests
- `204 No Content`: Successful DELETE requests
- `400 Bad Request`: Invalid request data or parameters
- `401 Unauthorized`: Authentication required or invalid token
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server-side error

## Code Examples

### JavaScript/React Examples

#### Authentication and Setup

```javascript
// Authentication
const login = async (username, password) => {
  const response = await fetch('/rest/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      user_name: username,
      password: password
    })
  });
  
  const data = await response.json();
  localStorage.setItem('access-token', JSON.stringify({
    token: data.token,
    expire: data.token_expire
  }));
  
  return data;
};

// API Client Setup
class DatagerryAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }
  
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
        ...options.headers
      },
      ...options
    };
    
    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  }
}
```

#### Fetching Objects

```javascript
// Get objects with pagination and filtering
const getObjects = async (api, params = {}) => {
  const queryParams = new URLSearchParams();
  
  if (params.limit) queryParams.append('limit', params.limit);
  if (params.page) queryParams.append('page', params.page);
  if (params.sort) queryParams.append('sort', params.sort);
  if (params.order) queryParams.append('order', params.order);
  if (params.filter) queryParams.append('filter', JSON.stringify(params.filter));
  
  const endpoint = `/objects/?${queryParams.toString()}`;
  return api.request(endpoint);
};

// Usage
const api = new DatagerryAPI('/rest', 'your-token-here');

const objects = await getObjects(api, {
  limit: 20,
  page: 1,
  sort: 'creation_time',
  order: -1,
  filter: { type_id: 1, active: true }
});

console.log(`Found ${objects.total} objects`);
objects.results.forEach(obj => {
  console.log(`Object ${obj.public_id}: ${obj.fields.find(f => f.name === 'hostname')?.value}`);
});
```

#### Creating Objects

```javascript
// Create a new object
const createObject = async (api, objectData) => {
  return api.request('/objects/', {
    method: 'POST',
    body: JSON.stringify(objectData)
  });
};

// Usage
const newObject = {
  type_id: 1,
  author_id: 1,
  active: true,
  fields: [
    { name: 'hostname', value: 'server01.example.com' },
    { name: 'ip_address', value: '*************' },
    { name: 'os', value: 'Ubuntu 20.04' }
  ]
};

const result = await createObject(api, newObject);
console.log(`Created object with ID: ${result.raw.public_id}`);
```

#### Updating Objects

```javascript
// Update an existing object
const updateObject = async (api, objectId, updateData) => {
  return api.request(`/objects/${objectId}`, {
    method: 'PUT',
    body: JSON.stringify(updateData)
  });
};

// Usage
const updatedData = {
  ...existingObject,
  fields: [
    { name: 'hostname', value: 'server01-updated.example.com' },
    { name: 'ip_address', value: '*************' }
  ]
};

const result = await updateObject(api, 1, updatedData);
console.log('Object updated successfully');
```

#### Working with Types

```javascript
// Get all types
const getTypes = async (api) => {
  return api.request('/types/');
};

// Create a new type
const createType = async (api, typeData) => {
  return api.request('/types/', {
    method: 'POST',
    body: JSON.stringify(typeData)
  });
};

// Usage
const newType = {
  name: 'network-device',
  label: 'Network Device',
  author_id: 1,
  active: true,
  fields: [
    {
      type: 'text',
      name: 'device_name',
      label: 'Device Name',
      required: true
    },
    {
      type: 'text',
      name: 'ip_address',
      label: 'IP Address',
      required: true
    },
    {
      type: 'select',
      name: 'device_type',
      label: 'Device Type',
      options: ['Router', 'Switch', 'Firewall']
    }
  ],
  render_meta: {
    icon: 'fas fa-network-wired',
    sections: []
  }
};

const typeResult = await createType(api, newType);
console.log(`Created type with ID: ${typeResult.raw.public_id}`);
```

## Best Practices

1. **Always use HTTPS** in production environments
2. **Store tokens securely** and implement token refresh logic
3. **Handle errors gracefully** with proper error handling
4. **Use pagination** for large datasets to avoid performance issues
5. **Implement proper filtering** to reduce data transfer
6. **Cache frequently accessed data** like types and categories
7. **Validate data** before sending to the API
8. **Use appropriate HTTP methods** (GET for reading, POST for creating, PUT/PATCH for updating, DELETE for removing)
9. **Include proper headers** for content type and authorization
10. **Monitor API rate limits** and implement retry logic if needed

## Rate Limiting and Performance

- Use pagination (`limit` and `page` parameters) for large datasets
- Implement client-side caching for static data like types and categories
- Use projection to limit returned fields when full objects aren't needed
- Consider using HEAD requests to get counts without transferring data
- Batch operations when possible to reduce API calls

This documentation provides a comprehensive guide for integrating with DATAGERRY's REST API in your React application. The examples show practical usage patterns that you can adapt to your specific needs.

---

*For the complete Hyperledger Fabric integration architecture and implementation guide, see [DATAGERRY-Fabric Integration Blueprint](DATAGERRY_Fabric_Integration_Blueprint.md).*
