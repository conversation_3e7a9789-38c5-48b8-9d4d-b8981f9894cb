//
//Copyright IBM Corp. All Rights Reserved.
//SPDX-License-Identifier: Apache-2.0

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.27.3
// source: test_plugin.proto

package testdata

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MarshaledSignedData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data      []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	Signature []byte `protobuf:"bytes,2,opt,name=signature,proto3" json:"signature,omitempty"`
	Identity  []byte `protobuf:"bytes,3,opt,name=identity,proto3" json:"identity,omitempty"`
}

func (x *MarshaledSignedData) Reset() {
	*x = MarshaledSignedData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_test_plugin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MarshaledSignedData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarshaledSignedData) ProtoMessage() {}

func (x *MarshaledSignedData) ProtoReflect() protoreflect.Message {
	mi := &file_test_plugin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarshaledSignedData.ProtoReflect.Descriptor instead.
func (*MarshaledSignedData) Descriptor() ([]byte, []int) {
	return file_test_plugin_proto_rawDescGZIP(), []int{0}
}

func (x *MarshaledSignedData) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *MarshaledSignedData) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *MarshaledSignedData) GetIdentity() []byte {
	if x != nil {
		return x.Identity
	}
	return nil
}

var File_test_plugin_proto protoreflect.FileDescriptor

var file_test_plugin_proto_rawDesc = []byte{
	0x0a, 0x11, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x08, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x22, 0x63, 0x0a,
	0x13, 0x4d, 0x61, 0x72, 0x73, 0x68, 0x61, 0x6c, 0x65, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x42, 0x47, 0x5a, 0x45, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x68, 0x79, 0x70, 0x65, 0x72, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x2f, 0x66, 0x61, 0x62,
	0x72, 0x69, 0x63, 0x2f, 0x63, 0x6f, 0x72, 0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x69, 0x74, 0x74,
	0x65, 0x72, 0x2f, 0x74, 0x78, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x76,
	0x32, 0x30, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_test_plugin_proto_rawDescOnce sync.Once
	file_test_plugin_proto_rawDescData = file_test_plugin_proto_rawDesc
)

func file_test_plugin_proto_rawDescGZIP() []byte {
	file_test_plugin_proto_rawDescOnce.Do(func() {
		file_test_plugin_proto_rawDescData = protoimpl.X.CompressGZIP(file_test_plugin_proto_rawDescData)
	})
	return file_test_plugin_proto_rawDescData
}

var file_test_plugin_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_test_plugin_proto_goTypes = []any{
	(*MarshaledSignedData)(nil), // 0: testdata.MarshaledSignedData
}
var file_test_plugin_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_test_plugin_proto_init() }
func file_test_plugin_proto_init() {
	if File_test_plugin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_test_plugin_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*MarshaledSignedData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_test_plugin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_test_plugin_proto_goTypes,
		DependencyIndexes: file_test_plugin_proto_depIdxs,
		MessageInfos:      file_test_plugin_proto_msgTypes,
	}.Build()
	File_test_plugin_proto = out.File
	file_test_plugin_proto_rawDesc = nil
	file_test_plugin_proto_goTypes = nil
	file_test_plugin_proto_depIdxs = nil
}
