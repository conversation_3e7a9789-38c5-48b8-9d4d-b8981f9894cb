# Copyright the Hyperledger Fabric contributors. All rights reserved.
#
# SPDX-License-Identifier: Apache-2.0

name: "Security vulnerability scan"

on:
  workflow_dispatch:
  schedule:
    - cron: "50 1 * * *"

permissions:
  # Required to upload SARIF file to CodeQL. See: https://github.com/github/codeql-action/issues/2117
  actions: read
  # Require writing security events to upload SARIF file to security tab
  security-events: write
  # to fetch code (actions/checkout)
  contents: read

jobs:
  latest:
    # Only run the scheduled job in hyperledger/fabric repository, not on personal forks
    if: github.event_name != 'schedule' || (github.event_name == 'schedule' && github.repository == 'hyperledger/fabric')
    strategy:
      fail-fast: false
      matrix:
        ref:
          - main
          - release-2.5
    uses: "google/osv-scanner-action/.github/workflows/osv-scanner-reusable.yml@main"
    with:
      scan-args: |-
        --lockfile=./go.mod
        --lockfile=./tools/go.mod
      matrix-property: ${{ matrix.ref }}-
      ref: ${{ matrix.ref }}

  get-latest-releases:
    # Only run the scheduled job in hyperledger/fabric repository, not on personal forks
    if: github.event_name != 'schedule' || (github.event_name == 'schedule' && github.repository == 'hyperledger/fabric')
    runs-on: ${{ github.repository == 'hyperledger/fabric' && 'fabric-ubuntu-24.04' || 'ubuntu-24.04' }}
    strategy:
      fail-fast: false
      matrix:
        ref:
          - branch: main
            name: main
          - branch: release-2.5
            name: release_25
    outputs:
      output_main: ${{ steps.latest_release.outputs.tag_main }}
      output_release-25: ${{ steps.latest_release.outputs.tag_release_25 }}
    steps:
      - name: Checkout ${{ matrix.ref.branch }} branch
        uses: actions/checkout@v4
        with:
          ref: ${{ matrix.ref.branch }}
          fetch-depth: 0
      - name: Get latest release
        id: latest_release
        run: |
          version="${{ matrix.ref.name }}"
          echo "tag_${version}=$(git -c versionsort.suffix=- tag --merged HEAD --sort=-version:refname | head -1)" >> "$GITHUB_OUTPUT"

  release:
    needs:
      - get-latest-releases
    if: github.event_name != 'schedule' || (github.event_name == 'schedule' && github.repository == 'hyperledger/fabric')
    strategy:
      fail-fast: false
      matrix:
        ref:
          - branch: main
            tag: ${{ needs.get-latest-releases.outputs.output_main }}
          - branch: release-2.5
            tag: ${{ needs.get-latest-releases.outputs.output_release-25 }}
    uses: "google/osv-scanner-action/.github/workflows/osv-scanner-reusable.yml@main"
    with:
      scan-args: |-
        --lockfile=./go.mod
        --lockfile=./tools/go.mod
      matrix-property: ${{ matrix.ref.tag }}-
      ref: ${{ matrix.ref.tag }}
