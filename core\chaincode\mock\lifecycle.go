// Code generated by counterfeiter. DO NOT EDIT.
package mock

import (
	"sync"

	"github.com/hyperledger/fabric/core/chaincode/lifecycle"
	"github.com/hyperledger/fabric/core/ledger"
)

type Lifecycle struct {
	ChaincodeEndorsementInfoStub        func(string, string, ledger.SimpleQueryExecutor) (*lifecycle.ChaincodeEndorsementInfo, error)
	chaincodeEndorsementInfoMutex       sync.RWMutex
	chaincodeEndorsementInfoArgsForCall []struct {
		arg1 string
		arg2 string
		arg3 ledger.SimpleQueryExecutor
	}
	chaincodeEndorsementInfoReturns struct {
		result1 *lifecycle.ChaincodeEndorsementInfo
		result2 error
	}
	chaincodeEndorsementInfoReturnsOnCall map[int]struct {
		result1 *lifecycle.ChaincodeEndorsementInfo
		result2 error
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *Lifecycle) ChaincodeEndorsementInfo(arg1 string, arg2 string, arg3 ledger.SimpleQueryExecutor) (*lifecycle.ChaincodeEndorsementInfo, error) {
	fake.chaincodeEndorsementInfoMutex.Lock()
	ret, specificReturn := fake.chaincodeEndorsementInfoReturnsOnCall[len(fake.chaincodeEndorsementInfoArgsForCall)]
	fake.chaincodeEndorsementInfoArgsForCall = append(fake.chaincodeEndorsementInfoArgsForCall, struct {
		arg1 string
		arg2 string
		arg3 ledger.SimpleQueryExecutor
	}{arg1, arg2, arg3})
	fake.recordInvocation("ChaincodeEndorsementInfo", []interface{}{arg1, arg2, arg3})
	fake.chaincodeEndorsementInfoMutex.Unlock()
	if fake.ChaincodeEndorsementInfoStub != nil {
		return fake.ChaincodeEndorsementInfoStub(arg1, arg2, arg3)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	fakeReturns := fake.chaincodeEndorsementInfoReturns
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *Lifecycle) ChaincodeEndorsementInfoCallCount() int {
	fake.chaincodeEndorsementInfoMutex.RLock()
	defer fake.chaincodeEndorsementInfoMutex.RUnlock()
	return len(fake.chaincodeEndorsementInfoArgsForCall)
}

func (fake *Lifecycle) ChaincodeEndorsementInfoCalls(stub func(string, string, ledger.SimpleQueryExecutor) (*lifecycle.ChaincodeEndorsementInfo, error)) {
	fake.chaincodeEndorsementInfoMutex.Lock()
	defer fake.chaincodeEndorsementInfoMutex.Unlock()
	fake.ChaincodeEndorsementInfoStub = stub
}

func (fake *Lifecycle) ChaincodeEndorsementInfoArgsForCall(i int) (string, string, ledger.SimpleQueryExecutor) {
	fake.chaincodeEndorsementInfoMutex.RLock()
	defer fake.chaincodeEndorsementInfoMutex.RUnlock()
	argsForCall := fake.chaincodeEndorsementInfoArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *Lifecycle) ChaincodeEndorsementInfoReturns(result1 *lifecycle.ChaincodeEndorsementInfo, result2 error) {
	fake.chaincodeEndorsementInfoMutex.Lock()
	defer fake.chaincodeEndorsementInfoMutex.Unlock()
	fake.ChaincodeEndorsementInfoStub = nil
	fake.chaincodeEndorsementInfoReturns = struct {
		result1 *lifecycle.ChaincodeEndorsementInfo
		result2 error
	}{result1, result2}
}

func (fake *Lifecycle) ChaincodeEndorsementInfoReturnsOnCall(i int, result1 *lifecycle.ChaincodeEndorsementInfo, result2 error) {
	fake.chaincodeEndorsementInfoMutex.Lock()
	defer fake.chaincodeEndorsementInfoMutex.Unlock()
	fake.ChaincodeEndorsementInfoStub = nil
	if fake.chaincodeEndorsementInfoReturnsOnCall == nil {
		fake.chaincodeEndorsementInfoReturnsOnCall = make(map[int]struct {
			result1 *lifecycle.ChaincodeEndorsementInfo
			result2 error
		})
	}
	fake.chaincodeEndorsementInfoReturnsOnCall[i] = struct {
		result1 *lifecycle.ChaincodeEndorsementInfo
		result2 error
	}{result1, result2}
}

func (fake *Lifecycle) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.chaincodeEndorsementInfoMutex.RLock()
	defer fake.chaincodeEndorsementInfoMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *Lifecycle) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}
