// Code generated by counterfeiter. DO NOT EDIT.
package fake

import (
	"sync"

	"github.com/hyperledger/fabric-protos-go-apiv2/common"
	"github.com/hyperledger/fabric/common/deliverclient"
	"github.com/hyperledger/fabric/common/deliverclient/blocksprovider"
)

type UpdatableBlockVerifier struct {
	CloneStub        func() deliverclient.CloneableUpdatableBlockVerifier
	cloneMutex       sync.RWMutex
	cloneArgsForCall []struct {
	}
	cloneReturns struct {
		result1 deliverclient.CloneableUpdatableBlockVerifier
	}
	cloneReturnsOnCall map[int]struct {
		result1 deliverclient.CloneableUpdatableBlockVerifier
	}
	UpdateBlockHeaderStub        func(*common.Block)
	updateBlockHeaderMutex       sync.RWMutex
	updateBlockHeaderArgsForCall []struct {
		arg1 *common.Block
	}
	UpdateConfigStub        func(*common.Block) error
	updateConfigMutex       sync.RWMutex
	updateConfigArgsForCall []struct {
		arg1 *common.Block
	}
	updateConfigReturns struct {
		result1 error
	}
	updateConfigReturnsOnCall map[int]struct {
		result1 error
	}
	VerifyBlockStub        func(*common.Block) error
	verifyBlockMutex       sync.RWMutex
	verifyBlockArgsForCall []struct {
		arg1 *common.Block
	}
	verifyBlockReturns struct {
		result1 error
	}
	verifyBlockReturnsOnCall map[int]struct {
		result1 error
	}
	VerifyBlockAttestationStub        func(*common.Block) error
	verifyBlockAttestationMutex       sync.RWMutex
	verifyBlockAttestationArgsForCall []struct {
		arg1 *common.Block
	}
	verifyBlockAttestationReturns struct {
		result1 error
	}
	verifyBlockAttestationReturnsOnCall map[int]struct {
		result1 error
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *UpdatableBlockVerifier) Clone() deliverclient.CloneableUpdatableBlockVerifier {
	fake.cloneMutex.Lock()
	ret, specificReturn := fake.cloneReturnsOnCall[len(fake.cloneArgsForCall)]
	fake.cloneArgsForCall = append(fake.cloneArgsForCall, struct {
	}{})
	stub := fake.CloneStub
	fakeReturns := fake.cloneReturns
	fake.recordInvocation("Clone", []interface{}{})
	fake.cloneMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *UpdatableBlockVerifier) CloneCallCount() int {
	fake.cloneMutex.RLock()
	defer fake.cloneMutex.RUnlock()
	return len(fake.cloneArgsForCall)
}

func (fake *UpdatableBlockVerifier) CloneCalls(stub func() deliverclient.CloneableUpdatableBlockVerifier) {
	fake.cloneMutex.Lock()
	defer fake.cloneMutex.Unlock()
	fake.CloneStub = stub
}

func (fake *UpdatableBlockVerifier) CloneReturns(result1 deliverclient.CloneableUpdatableBlockVerifier) {
	fake.cloneMutex.Lock()
	defer fake.cloneMutex.Unlock()
	fake.CloneStub = nil
	fake.cloneReturns = struct {
		result1 deliverclient.CloneableUpdatableBlockVerifier
	}{result1}
}

func (fake *UpdatableBlockVerifier) CloneReturnsOnCall(i int, result1 deliverclient.CloneableUpdatableBlockVerifier) {
	fake.cloneMutex.Lock()
	defer fake.cloneMutex.Unlock()
	fake.CloneStub = nil
	if fake.cloneReturnsOnCall == nil {
		fake.cloneReturnsOnCall = make(map[int]struct {
			result1 deliverclient.CloneableUpdatableBlockVerifier
		})
	}
	fake.cloneReturnsOnCall[i] = struct {
		result1 deliverclient.CloneableUpdatableBlockVerifier
	}{result1}
}

func (fake *UpdatableBlockVerifier) UpdateBlockHeader(arg1 *common.Block) {
	fake.updateBlockHeaderMutex.Lock()
	fake.updateBlockHeaderArgsForCall = append(fake.updateBlockHeaderArgsForCall, struct {
		arg1 *common.Block
	}{arg1})
	stub := fake.UpdateBlockHeaderStub
	fake.recordInvocation("UpdateBlockHeader", []interface{}{arg1})
	fake.updateBlockHeaderMutex.Unlock()
	if stub != nil {
		fake.UpdateBlockHeaderStub(arg1)
	}
}

func (fake *UpdatableBlockVerifier) UpdateBlockHeaderCallCount() int {
	fake.updateBlockHeaderMutex.RLock()
	defer fake.updateBlockHeaderMutex.RUnlock()
	return len(fake.updateBlockHeaderArgsForCall)
}

func (fake *UpdatableBlockVerifier) UpdateBlockHeaderCalls(stub func(*common.Block)) {
	fake.updateBlockHeaderMutex.Lock()
	defer fake.updateBlockHeaderMutex.Unlock()
	fake.UpdateBlockHeaderStub = stub
}

func (fake *UpdatableBlockVerifier) UpdateBlockHeaderArgsForCall(i int) *common.Block {
	fake.updateBlockHeaderMutex.RLock()
	defer fake.updateBlockHeaderMutex.RUnlock()
	argsForCall := fake.updateBlockHeaderArgsForCall[i]
	return argsForCall.arg1
}

func (fake *UpdatableBlockVerifier) UpdateConfig(arg1 *common.Block) error {
	fake.updateConfigMutex.Lock()
	ret, specificReturn := fake.updateConfigReturnsOnCall[len(fake.updateConfigArgsForCall)]
	fake.updateConfigArgsForCall = append(fake.updateConfigArgsForCall, struct {
		arg1 *common.Block
	}{arg1})
	stub := fake.UpdateConfigStub
	fakeReturns := fake.updateConfigReturns
	fake.recordInvocation("UpdateConfig", []interface{}{arg1})
	fake.updateConfigMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *UpdatableBlockVerifier) UpdateConfigCallCount() int {
	fake.updateConfigMutex.RLock()
	defer fake.updateConfigMutex.RUnlock()
	return len(fake.updateConfigArgsForCall)
}

func (fake *UpdatableBlockVerifier) UpdateConfigCalls(stub func(*common.Block) error) {
	fake.updateConfigMutex.Lock()
	defer fake.updateConfigMutex.Unlock()
	fake.UpdateConfigStub = stub
}

func (fake *UpdatableBlockVerifier) UpdateConfigArgsForCall(i int) *common.Block {
	fake.updateConfigMutex.RLock()
	defer fake.updateConfigMutex.RUnlock()
	argsForCall := fake.updateConfigArgsForCall[i]
	return argsForCall.arg1
}

func (fake *UpdatableBlockVerifier) UpdateConfigReturns(result1 error) {
	fake.updateConfigMutex.Lock()
	defer fake.updateConfigMutex.Unlock()
	fake.UpdateConfigStub = nil
	fake.updateConfigReturns = struct {
		result1 error
	}{result1}
}

func (fake *UpdatableBlockVerifier) UpdateConfigReturnsOnCall(i int, result1 error) {
	fake.updateConfigMutex.Lock()
	defer fake.updateConfigMutex.Unlock()
	fake.UpdateConfigStub = nil
	if fake.updateConfigReturnsOnCall == nil {
		fake.updateConfigReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.updateConfigReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *UpdatableBlockVerifier) VerifyBlock(arg1 *common.Block) error {
	fake.verifyBlockMutex.Lock()
	ret, specificReturn := fake.verifyBlockReturnsOnCall[len(fake.verifyBlockArgsForCall)]
	fake.verifyBlockArgsForCall = append(fake.verifyBlockArgsForCall, struct {
		arg1 *common.Block
	}{arg1})
	stub := fake.VerifyBlockStub
	fakeReturns := fake.verifyBlockReturns
	fake.recordInvocation("VerifyBlock", []interface{}{arg1})
	fake.verifyBlockMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *UpdatableBlockVerifier) VerifyBlockCallCount() int {
	fake.verifyBlockMutex.RLock()
	defer fake.verifyBlockMutex.RUnlock()
	return len(fake.verifyBlockArgsForCall)
}

func (fake *UpdatableBlockVerifier) VerifyBlockCalls(stub func(*common.Block) error) {
	fake.verifyBlockMutex.Lock()
	defer fake.verifyBlockMutex.Unlock()
	fake.VerifyBlockStub = stub
}

func (fake *UpdatableBlockVerifier) VerifyBlockArgsForCall(i int) *common.Block {
	fake.verifyBlockMutex.RLock()
	defer fake.verifyBlockMutex.RUnlock()
	argsForCall := fake.verifyBlockArgsForCall[i]
	return argsForCall.arg1
}

func (fake *UpdatableBlockVerifier) VerifyBlockReturns(result1 error) {
	fake.verifyBlockMutex.Lock()
	defer fake.verifyBlockMutex.Unlock()
	fake.VerifyBlockStub = nil
	fake.verifyBlockReturns = struct {
		result1 error
	}{result1}
}

func (fake *UpdatableBlockVerifier) VerifyBlockReturnsOnCall(i int, result1 error) {
	fake.verifyBlockMutex.Lock()
	defer fake.verifyBlockMutex.Unlock()
	fake.VerifyBlockStub = nil
	if fake.verifyBlockReturnsOnCall == nil {
		fake.verifyBlockReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.verifyBlockReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *UpdatableBlockVerifier) VerifyBlockAttestation(arg1 *common.Block) error {
	fake.verifyBlockAttestationMutex.Lock()
	ret, specificReturn := fake.verifyBlockAttestationReturnsOnCall[len(fake.verifyBlockAttestationArgsForCall)]
	fake.verifyBlockAttestationArgsForCall = append(fake.verifyBlockAttestationArgsForCall, struct {
		arg1 *common.Block
	}{arg1})
	stub := fake.VerifyBlockAttestationStub
	fakeReturns := fake.verifyBlockAttestationReturns
	fake.recordInvocation("VerifyBlockAttestation", []interface{}{arg1})
	fake.verifyBlockAttestationMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *UpdatableBlockVerifier) VerifyBlockAttestationCallCount() int {
	fake.verifyBlockAttestationMutex.RLock()
	defer fake.verifyBlockAttestationMutex.RUnlock()
	return len(fake.verifyBlockAttestationArgsForCall)
}

func (fake *UpdatableBlockVerifier) VerifyBlockAttestationCalls(stub func(*common.Block) error) {
	fake.verifyBlockAttestationMutex.Lock()
	defer fake.verifyBlockAttestationMutex.Unlock()
	fake.VerifyBlockAttestationStub = stub
}

func (fake *UpdatableBlockVerifier) VerifyBlockAttestationArgsForCall(i int) *common.Block {
	fake.verifyBlockAttestationMutex.RLock()
	defer fake.verifyBlockAttestationMutex.RUnlock()
	argsForCall := fake.verifyBlockAttestationArgsForCall[i]
	return argsForCall.arg1
}

func (fake *UpdatableBlockVerifier) VerifyBlockAttestationReturns(result1 error) {
	fake.verifyBlockAttestationMutex.Lock()
	defer fake.verifyBlockAttestationMutex.Unlock()
	fake.VerifyBlockAttestationStub = nil
	fake.verifyBlockAttestationReturns = struct {
		result1 error
	}{result1}
}

func (fake *UpdatableBlockVerifier) VerifyBlockAttestationReturnsOnCall(i int, result1 error) {
	fake.verifyBlockAttestationMutex.Lock()
	defer fake.verifyBlockAttestationMutex.Unlock()
	fake.VerifyBlockAttestationStub = nil
	if fake.verifyBlockAttestationReturnsOnCall == nil {
		fake.verifyBlockAttestationReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.verifyBlockAttestationReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *UpdatableBlockVerifier) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.cloneMutex.RLock()
	defer fake.cloneMutex.RUnlock()
	fake.updateBlockHeaderMutex.RLock()
	defer fake.updateBlockHeaderMutex.RUnlock()
	fake.updateConfigMutex.RLock()
	defer fake.updateConfigMutex.RUnlock()
	fake.verifyBlockMutex.RLock()
	defer fake.verifyBlockMutex.RUnlock()
	fake.verifyBlockAttestationMutex.RLock()
	defer fake.verifyBlockAttestationMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *UpdatableBlockVerifier) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ blocksprovider.UpdatableBlockVerifier = new(UpdatableBlockVerifier)
