//
//Copyright IBM Corp. All Rights Reserved.
//SPDX-License-Identifier: Apache-2.0

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.27.3
// source: ccprovider.proto

package ccprovider

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// -------- ChaincodeData is stored on the LSCC -------
// ChaincodeData defines the datastructure for chaincodes to be serialized by proto
// Type provides an additional check by directing to use a specific package after instantiation
// Data is Type specific (see CDSPackage and SignedCDSPackage)
type ChaincodeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of the chaincode
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Version of the chaincode
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// Escc for the chaincode instance
	Escc string `protobuf:"bytes,3,opt,name=escc,proto3" json:"escc,omitempty"`
	// Vscc for the chaincode instance
	Vscc string `protobuf:"bytes,4,opt,name=vscc,proto3" json:"vscc,omitempty"`
	// Policy endorsement policy for the chaincode instance
	Policy []byte `protobuf:"bytes,5,opt,name=policy,proto3" json:"policy,omitempty"`
	// Data data specific to the package
	Data []byte `protobuf:"bytes,6,opt,name=data,proto3" json:"data,omitempty"`
	// Id of the chaincode that's the unique fingerprint for the CC This is not
	// currently used anywhere but serves as a good eyecatcher
	Id []byte `protobuf:"bytes,7,opt,name=id,proto3" json:"id,omitempty"`
	// InstantiationPolicy for the chaincode
	InstantiationPolicy []byte `protobuf:"bytes,8,opt,name=instantiation_policy,json=instantiationPolicy,proto3" json:"instantiation_policy,omitempty"`
}

func (x *ChaincodeData) Reset() {
	*x = ChaincodeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ccprovider_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChaincodeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChaincodeData) ProtoMessage() {}

func (x *ChaincodeData) ProtoReflect() protoreflect.Message {
	mi := &file_ccprovider_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChaincodeData.ProtoReflect.Descriptor instead.
func (*ChaincodeData) Descriptor() ([]byte, []int) {
	return file_ccprovider_proto_rawDescGZIP(), []int{0}
}

func (x *ChaincodeData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChaincodeData) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ChaincodeData) GetEscc() string {
	if x != nil {
		return x.Escc
	}
	return ""
}

func (x *ChaincodeData) GetVscc() string {
	if x != nil {
		return x.Vscc
	}
	return ""
}

func (x *ChaincodeData) GetPolicy() []byte {
	if x != nil {
		return x.Policy
	}
	return nil
}

func (x *ChaincodeData) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ChaincodeData) GetId() []byte {
	if x != nil {
		return x.Id
	}
	return nil
}

func (x *ChaincodeData) GetInstantiationPolicy() []byte {
	if x != nil {
		return x.InstantiationPolicy
	}
	return nil
}

// ----- CDSData ------
// CDSData is data stored in the LSCC on instantiation of a CC
// for CDSPackage.  This needs to be serialized for ChaincodeData
// hence the protobuf format
type CDSData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// CodeHash hash of CodePackage from ChaincodeDeploymentSpec
	CodeHash []byte `protobuf:"bytes,1,opt,name=code_hash,json=codeHash,proto3" json:"code_hash,omitempty"`
	// MetaDataHash hash of Name and Version from ChaincodeDeploymentSpec
	MetaDataHash []byte `protobuf:"bytes,2,opt,name=meta_data_hash,json=metaDataHash,proto3" json:"meta_data_hash,omitempty"`
}

func (x *CDSData) Reset() {
	*x = CDSData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ccprovider_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDSData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDSData) ProtoMessage() {}

func (x *CDSData) ProtoReflect() protoreflect.Message {
	mi := &file_ccprovider_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDSData.ProtoReflect.Descriptor instead.
func (*CDSData) Descriptor() ([]byte, []int) {
	return file_ccprovider_proto_rawDescGZIP(), []int{1}
}

func (x *CDSData) GetCodeHash() []byte {
	if x != nil {
		return x.CodeHash
	}
	return nil
}

func (x *CDSData) GetMetaDataHash() []byte {
	if x != nil {
		return x.MetaDataHash
	}
	return nil
}

// ----- SignedCDSData ------
// SignedCDSData is data stored in the LSCC on instantiation of a CC
// for SignedCDSPackage. This needs to be serialized for ChaincodeData
// hence the protobuf format
type SignedCDSData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CodeHash      []byte `protobuf:"bytes,1,opt,name=code_hash,json=codeHash,proto3" json:"code_hash,omitempty"`
	MetaDataHash  []byte `protobuf:"bytes,2,opt,name=meta_data_hash,json=metaDataHash,proto3" json:"meta_data_hash,omitempty"`
	SignatureHash []byte `protobuf:"bytes,3,opt,name=signature_hash,json=signatureHash,proto3" json:"signature_hash,omitempty"`
}

func (x *SignedCDSData) Reset() {
	*x = SignedCDSData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ccprovider_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignedCDSData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignedCDSData) ProtoMessage() {}

func (x *SignedCDSData) ProtoReflect() protoreflect.Message {
	mi := &file_ccprovider_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignedCDSData.ProtoReflect.Descriptor instead.
func (*SignedCDSData) Descriptor() ([]byte, []int) {
	return file_ccprovider_proto_rawDescGZIP(), []int{2}
}

func (x *SignedCDSData) GetCodeHash() []byte {
	if x != nil {
		return x.CodeHash
	}
	return nil
}

func (x *SignedCDSData) GetMetaDataHash() []byte {
	if x != nil {
		return x.MetaDataHash
	}
	return nil
}

func (x *SignedCDSData) GetSignatureHash() []byte {
	if x != nil {
		return x.SignatureHash
	}
	return nil
}

var File_ccprovider_proto protoreflect.FileDescriptor

var file_ccprovider_proto_rawDesc = []byte{
	0x0a, 0x10, 0x63, 0x63, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x63, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0xd4,
	0x01, 0x0a, 0x0d, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x65, 0x73, 0x63, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x65, 0x73,
	0x63, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x73, 0x63, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x76, 0x73, 0x63, 0x63, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x31, 0x0a, 0x14, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x69, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x13, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x22, 0x4c, 0x0a, 0x07, 0x43, 0x44, 0x53, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x08, 0x63, 0x6f, 0x64, 0x65, 0x48, 0x61, 0x73, 0x68, 0x12, 0x24, 0x0a,
	0x0e, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x48,
	0x61, 0x73, 0x68, 0x22, 0x79, 0x0a, 0x0d, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x43, 0x44, 0x53,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x68, 0x61, 0x73,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x63, 0x6f, 0x64, 0x65, 0x48, 0x61, 0x73,
	0x68, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x68,
	0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x6d, 0x65, 0x74, 0x61, 0x44,
	0x61, 0x74, 0x61, 0x48, 0x61, 0x73, 0x68, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0d, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x48, 0x61, 0x73, 0x68, 0x42, 0x36,
	0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x68, 0x79, 0x70,
	0x65, 0x72, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x72, 0x2f, 0x66, 0x61, 0x62, 0x72, 0x69, 0x63, 0x2f,
	0x63, 0x6f, 0x72, 0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x63, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ccprovider_proto_rawDescOnce sync.Once
	file_ccprovider_proto_rawDescData = file_ccprovider_proto_rawDesc
)

func file_ccprovider_proto_rawDescGZIP() []byte {
	file_ccprovider_proto_rawDescOnce.Do(func() {
		file_ccprovider_proto_rawDescData = protoimpl.X.CompressGZIP(file_ccprovider_proto_rawDescData)
	})
	return file_ccprovider_proto_rawDescData
}

var file_ccprovider_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_ccprovider_proto_goTypes = []any{
	(*ChaincodeData)(nil), // 0: ccprovider.ChaincodeData
	(*CDSData)(nil),       // 1: ccprovider.CDSData
	(*SignedCDSData)(nil), // 2: ccprovider.SignedCDSData
}
var file_ccprovider_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_ccprovider_proto_init() }
func file_ccprovider_proto_init() {
	if File_ccprovider_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ccprovider_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*ChaincodeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ccprovider_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*CDSData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ccprovider_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*SignedCDSData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ccprovider_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ccprovider_proto_goTypes,
		DependencyIndexes: file_ccprovider_proto_depIdxs,
		MessageInfos:      file_ccprovider_proto_msgTypes,
	}.Build()
	File_ccprovider_proto = out.File
	file_ccprovider_proto_rawDesc = nil
	file_ccprovider_proto_goTypes = nil
	file_ccprovider_proto_depIdxs = nil
}
