// Code generated by counterfeiter. DO NOT EDIT.
package fake

import (
	"sync"

	"github.com/hyperledger/fabric/common/deliverclient/blocksprovider"
)

type CensorshipDetector struct {
	ErrorsChannelStub        func() <-chan error
	errorsChannelMutex       sync.RWMutex
	errorsChannelArgsForCall []struct {
	}
	errorsChannelReturns struct {
		result1 <-chan error
	}
	errorsChannelReturnsOnCall map[int]struct {
		result1 <-chan error
	}
	MonitorStub        func()
	monitorMutex       sync.RWMutex
	monitorArgsForCall []struct {
	}
	StopStub        func()
	stopMutex       sync.RWMutex
	stopArgsForCall []struct {
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *CensorshipDetector) ErrorsChannel() <-chan error {
	fake.errorsChannelMutex.Lock()
	ret, specificReturn := fake.errorsChannelReturnsOnCall[len(fake.errorsChannelArgsForCall)]
	fake.errorsChannelArgsForCall = append(fake.errorsChannelArgsForCall, struct {
	}{})
	stub := fake.ErrorsChannelStub
	fakeReturns := fake.errorsChannelReturns
	fake.recordInvocation("ErrorsChannel", []interface{}{})
	fake.errorsChannelMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *CensorshipDetector) ErrorsChannelCallCount() int {
	fake.errorsChannelMutex.RLock()
	defer fake.errorsChannelMutex.RUnlock()
	return len(fake.errorsChannelArgsForCall)
}

func (fake *CensorshipDetector) ErrorsChannelCalls(stub func() <-chan error) {
	fake.errorsChannelMutex.Lock()
	defer fake.errorsChannelMutex.Unlock()
	fake.ErrorsChannelStub = stub
}

func (fake *CensorshipDetector) ErrorsChannelReturns(result1 <-chan error) {
	fake.errorsChannelMutex.Lock()
	defer fake.errorsChannelMutex.Unlock()
	fake.ErrorsChannelStub = nil
	fake.errorsChannelReturns = struct {
		result1 <-chan error
	}{result1}
}

func (fake *CensorshipDetector) ErrorsChannelReturnsOnCall(i int, result1 <-chan error) {
	fake.errorsChannelMutex.Lock()
	defer fake.errorsChannelMutex.Unlock()
	fake.ErrorsChannelStub = nil
	if fake.errorsChannelReturnsOnCall == nil {
		fake.errorsChannelReturnsOnCall = make(map[int]struct {
			result1 <-chan error
		})
	}
	fake.errorsChannelReturnsOnCall[i] = struct {
		result1 <-chan error
	}{result1}
}

func (fake *CensorshipDetector) Monitor() {
	fake.monitorMutex.Lock()
	fake.monitorArgsForCall = append(fake.monitorArgsForCall, struct {
	}{})
	stub := fake.MonitorStub
	fake.recordInvocation("Monitor", []interface{}{})
	fake.monitorMutex.Unlock()
	if stub != nil {
		fake.MonitorStub()
	}
}

func (fake *CensorshipDetector) MonitorCallCount() int {
	fake.monitorMutex.RLock()
	defer fake.monitorMutex.RUnlock()
	return len(fake.monitorArgsForCall)
}

func (fake *CensorshipDetector) MonitorCalls(stub func()) {
	fake.monitorMutex.Lock()
	defer fake.monitorMutex.Unlock()
	fake.MonitorStub = stub
}

func (fake *CensorshipDetector) Stop() {
	fake.stopMutex.Lock()
	fake.stopArgsForCall = append(fake.stopArgsForCall, struct {
	}{})
	stub := fake.StopStub
	fake.recordInvocation("Stop", []interface{}{})
	fake.stopMutex.Unlock()
	if stub != nil {
		fake.StopStub()
	}
}

func (fake *CensorshipDetector) StopCallCount() int {
	fake.stopMutex.RLock()
	defer fake.stopMutex.RUnlock()
	return len(fake.stopArgsForCall)
}

func (fake *CensorshipDetector) StopCalls(stub func()) {
	fake.stopMutex.Lock()
	defer fake.stopMutex.Unlock()
	fake.StopStub = stub
}

func (fake *CensorshipDetector) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.errorsChannelMutex.RLock()
	defer fake.errorsChannelMutex.RUnlock()
	fake.monitorMutex.RLock()
	defer fake.monitorMutex.RUnlock()
	fake.stopMutex.RLock()
	defer fake.stopMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *CensorshipDetector) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ blocksprovider.CensorshipDetector = new(CensorshipDetector)
