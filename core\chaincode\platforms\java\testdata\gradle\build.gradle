plugins {
  id 'com.github.johnrengelman.shadow' version '2.0.3'
  id 'java'
}

group 'org.hyperledger.fabric-chaincode-java'
version '1.0-SNAPSHOT'

sourceCompatibility = 1.8

repositories {
  mavenLocal()
  mavenCentral()
}

dependencies {
  compile group: 'org.hyperledger.fabric-chaincode-java', name: 'fabric-chaincode-shim', version: '1.3.0-SNAPSHOT'
  testCompile group: 'junit', name: 'junit', version: '4.12'
}

shadowJar {
  baseName = 'chaincode'
  version = null
  classifier = null

  manifest {
    attributes 'Main-Class': 'example.ExampleCC'
  }
}