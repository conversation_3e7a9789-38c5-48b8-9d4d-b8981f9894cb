// Code generated by counterfeiter. DO NOT EDIT.
package mock

import (
	"sync"

	"github.com/hyperledger/fabric/core/container/ccintf"
)

type ChaincodeStreamHandler struct {
	HandleChaincodeStreamStub        func(ccintf.ChaincodeStream) error
	handleChaincodeStreamMutex       sync.RWMutex
	handleChaincodeStreamArgsForCall []struct {
		arg1 ccintf.ChaincodeStream
	}
	handleChaincodeStreamReturns struct {
		result1 error
	}
	handleChaincodeStreamReturnsOnCall map[int]struct {
		result1 error
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *ChaincodeStreamHandler) HandleChaincodeStream(arg1 ccintf.ChaincodeStream) error {
	fake.handleChaincodeStreamMutex.Lock()
	ret, specificReturn := fake.handleChaincodeStreamReturnsOnCall[len(fake.handleChaincodeStreamArgsForCall)]
	fake.handleChaincodeStreamArgsForCall = append(fake.handleChaincodeStreamArgsForCall, struct {
		arg1 ccintf.ChaincodeStream
	}{arg1})
	fake.recordInvocation("HandleChaincodeStream", []interface{}{arg1})
	fake.handleChaincodeStreamMutex.Unlock()
	if fake.HandleChaincodeStreamStub != nil {
		return fake.HandleChaincodeStreamStub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	fakeReturns := fake.handleChaincodeStreamReturns
	return fakeReturns.result1
}

func (fake *ChaincodeStreamHandler) HandleChaincodeStreamCallCount() int {
	fake.handleChaincodeStreamMutex.RLock()
	defer fake.handleChaincodeStreamMutex.RUnlock()
	return len(fake.handleChaincodeStreamArgsForCall)
}

func (fake *ChaincodeStreamHandler) HandleChaincodeStreamCalls(stub func(ccintf.ChaincodeStream) error) {
	fake.handleChaincodeStreamMutex.Lock()
	defer fake.handleChaincodeStreamMutex.Unlock()
	fake.HandleChaincodeStreamStub = stub
}

func (fake *ChaincodeStreamHandler) HandleChaincodeStreamArgsForCall(i int) ccintf.ChaincodeStream {
	fake.handleChaincodeStreamMutex.RLock()
	defer fake.handleChaincodeStreamMutex.RUnlock()
	argsForCall := fake.handleChaincodeStreamArgsForCall[i]
	return argsForCall.arg1
}

func (fake *ChaincodeStreamHandler) HandleChaincodeStreamReturns(result1 error) {
	fake.handleChaincodeStreamMutex.Lock()
	defer fake.handleChaincodeStreamMutex.Unlock()
	fake.HandleChaincodeStreamStub = nil
	fake.handleChaincodeStreamReturns = struct {
		result1 error
	}{result1}
}

func (fake *ChaincodeStreamHandler) HandleChaincodeStreamReturnsOnCall(i int, result1 error) {
	fake.handleChaincodeStreamMutex.Lock()
	defer fake.handleChaincodeStreamMutex.Unlock()
	fake.HandleChaincodeStreamStub = nil
	if fake.handleChaincodeStreamReturnsOnCall == nil {
		fake.handleChaincodeStreamReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.handleChaincodeStreamReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *ChaincodeStreamHandler) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.handleChaincodeStreamMutex.RLock()
	defer fake.handleChaincodeStreamMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *ChaincodeStreamHandler) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}
