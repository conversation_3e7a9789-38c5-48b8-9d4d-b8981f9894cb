// Code generated by counterfeiter. DO NOT EDIT.
package mock

import (
	"io"
	"sync"

	"github.com/hyperledger/fabric/core/container"
)

type ExternalBuilder struct {
	BuildStub        func(string, []byte, io.Reader) (container.Instance, error)
	buildMutex       sync.RWMutex
	buildArgsForCall []struct {
		arg1 string
		arg2 []byte
		arg3 io.Reader
	}
	buildReturns struct {
		result1 container.Instance
		result2 error
	}
	buildReturnsOnCall map[int]struct {
		result1 container.Instance
		result2 error
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *ExternalBuilder) Build(arg1 string, arg2 []byte, arg3 io.Reader) (container.Instance, error) {
	var arg2Copy []byte
	if arg2 != nil {
		arg2Copy = make([]byte, len(arg2))
		copy(arg2Copy, arg2)
	}
	fake.buildMutex.Lock()
	ret, specificReturn := fake.buildReturnsOnCall[len(fake.buildArgsForCall)]
	fake.buildArgsForCall = append(fake.buildArgsForCall, struct {
		arg1 string
		arg2 []byte
		arg3 io.Reader
	}{arg1, arg2Copy, arg3})
	fake.recordInvocation("Build", []interface{}{arg1, arg2Copy, arg3})
	fake.buildMutex.Unlock()
	if fake.BuildStub != nil {
		return fake.BuildStub(arg1, arg2, arg3)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	fakeReturns := fake.buildReturns
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *ExternalBuilder) BuildCallCount() int {
	fake.buildMutex.RLock()
	defer fake.buildMutex.RUnlock()
	return len(fake.buildArgsForCall)
}

func (fake *ExternalBuilder) BuildCalls(stub func(string, []byte, io.Reader) (container.Instance, error)) {
	fake.buildMutex.Lock()
	defer fake.buildMutex.Unlock()
	fake.BuildStub = stub
}

func (fake *ExternalBuilder) BuildArgsForCall(i int) (string, []byte, io.Reader) {
	fake.buildMutex.RLock()
	defer fake.buildMutex.RUnlock()
	argsForCall := fake.buildArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *ExternalBuilder) BuildReturns(result1 container.Instance, result2 error) {
	fake.buildMutex.Lock()
	defer fake.buildMutex.Unlock()
	fake.BuildStub = nil
	fake.buildReturns = struct {
		result1 container.Instance
		result2 error
	}{result1, result2}
}

func (fake *ExternalBuilder) BuildReturnsOnCall(i int, result1 container.Instance, result2 error) {
	fake.buildMutex.Lock()
	defer fake.buildMutex.Unlock()
	fake.BuildStub = nil
	if fake.buildReturnsOnCall == nil {
		fake.buildReturnsOnCall = make(map[int]struct {
			result1 container.Instance
			result2 error
		})
	}
	fake.buildReturnsOnCall[i] = struct {
		result1 container.Instance
		result2 error
	}{result1, result2}
}

func (fake *ExternalBuilder) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.buildMutex.RLock()
	defer fake.buildMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *ExternalBuilder) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ container.ExternalBuilder = new(ExternalBuilder)
