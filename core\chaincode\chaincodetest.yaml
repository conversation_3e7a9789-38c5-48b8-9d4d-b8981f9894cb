# Copyright IBM Corp. All Rights Reserved.
#
# SPDX-License-Identifier: Apache-2.0
#

# CA server parameters
#
server:
        # current version of the CA
        version: "0.1"

        # limits the number of operating system threads used by the CA
        gomaxprocs: 2

        # path to the OBC state directory and CA state subdirectory
        # rootpath: "."
        # cadir: ".ca"

        # port the CA services are listening on
        port: ":20051"

        # TLS certificate and key file paths
        tls:

security:
    # Can be 256 or 384
    # Must be the same as in core.yaml
    level: 256

# Enabling/disabling different logging levels of the CA.
#
logging:
        trace: 0
        info: 1
        warning: 1
        error: 1
        panic: 1

# Enable attribute encryption in TCerts generated by TCA
tca:
    attribute-encryption:
       enabled: true

# Default attributes for Attribute Certificate Authority
aca:
    attributes:
        attribute-entry-0: user1;bank_a;company;ACompany;2015-01-01T00:00:00-03:00;;
        attribute-entry-1: user1;bank_a;position;Software Staff;2015-01-01T00:00:00-03:00;2015-07-12T23:59:59-03:00;
        attribute-entry-2: user1;bank_a;position;Software Engineer;2015-07-13T00:00:00-03:00;;
        attribute-entry-3: user2;bank_a;company;ACompany;2001-02-02T00:00:00-03:00;;
        attribute-entry-4: user2;bank_a;position;Project Manager;2001-02-02T00:00:00-03:00;;
    address: localhost:20051
    server-name: acap
    enabled: true

# Default users to be registered with the CA on first launch.  The role is a binary OR
# of the different roles a user can have:
#
# - simple client such as a wallet: CLIENT
# - non-validating peer: PEER
# - validating client: VALIDATOR
# - auditing client: AUDITOR
#
eca:
        affiliations:
           banks_and_institutions:
              banks:
                  - bank_a
                  - bank_b
                  - bank_c
              institutions:
                  - institution_a
        users:
                # <EnrollmentID>: <role (1:client, 2: peer, 4: validator, 8: auditor)> <EnrollmentPWD> <Affiliation> <Affiliation_Role>
                lukas: 1 NPKYL39uKbkj institution_a
                diego: 1 DRJ23pEQl16a institution_a
                jim: 1 6avZQLwcUe9b institution_a

                vp: 4 f3489fy98ghf

###############################################################################
#
#    CLI section
#
###############################################################################
cli:

    # The address that the cli process will use for callbacks from chaincodes
    address: 0.0.0.0:7052



###############################################################################
#
#    REST section
#
###############################################################################
rest:

    # Enable/disable setting for the REST service. It is recommended to disable
    # REST service on validators in production deployment and use non-validating
    # nodes to host REST service
    enabled: true

    # The address that the REST service will listen on for incoming requests.
    address: 0.0.0.0:7050


###############################################################################
#
#    Peer section
#
###############################################################################
peer:

    # Peer Version following version semantics as described here http://semver.org/
    # The Peer supplies this version in communications with other Peers
    version:  0.1.0

    # The Peer id is used for identifying this Peer instance.
    id: jdoe

    # The privateKey to be used by this peer
    # privateKey: 794ef087680e2494fa4918fd8fb80fb284b50b57d321a31423fe42b9ccf6216047cea0b66fe8365a8e3f2a8140c6866cc45852e63124668bee1daa9c97da0c2a

    # The networkId allows for logical separation of networks
    # networkId: dev
    # networkId: test
    networkId: dev

    # The Address this Peer will listen on
    listenAddress: 0.0.0.0:21212
    # The Address this Peer will bind to for providing services
    address: 0.0.0.0:21212
    # Whether the Peer should programmatically determine the address to bind to.
    # This case is useful for docker containers.
    addressAutoDetect: true

    # Peer port to accept connections on
    port:    21212
    workers: 2

    # Sync related configuration
    sync:
        blocks:
            # Channel size for readonly SyncBlocks messages channel for receiving
            # blocks from oppositie Peer Endpoints.
            # NOTE: currently messages are not stored and forwarded, but rather
            # lost if the channel write blocks.
            channelSize: 10
        state:
            snapshot:
                # Channel size for readonly syncStateSnapshot messages channel
                # for receiving state deltas for snapshot from oppositie Peer Endpoints.
                # NOTE: currently messages are not stored and forwarded, but
                # rather lost if the channel write blocks.
                channelSize: 50
            deltas:
                # Channel size for readonly syncStateDeltas messages channel for
                # receiving state deltas for a syncBlockRange from oppositie
                # Peer Endpoints.
                # NOTE: currently messages are not stored and forwarded,
                # but rather lost if the channel write blocks.
                channelSize: 20

    # Validator defines whether this peer is a validating peer or not, and if
    # it is enabled, what consensus plugin to load
    validator:
        enabled: true

        consensus:
            # Consensus plugin to use. The value is the name of the plugin, e.g. pbft, noops ( this value is case-insensitive)
            # if the given value is not recognized, we will default to noops
            plugin: noops

            # total number of consensus messages which will be buffered per connection before delivery is rejected
            buffersize: 1000

    # TLS Settings for p2p communications
    tls:
        enabled:  true
        cert:
            file: testdata/server1.pem
        key:
            file: testdata/server1.key

        # The server name use to verify the hostname returned by TLS handshake
        # The key cert was generated using
        #       openssl req -x509 -nodes -days 3650 -newkey rsa:2048 -keyout testdata/server1.key -out testdata/server1.pem
        serverhostoverride: dummy

    # PKI member services properties
    pki:
        eca:
            paddr: localhost:20051
        tca:
            paddr: localhost:20051
        tlsca:
            paddr: localhost:20051
        tls:
            enabled: false
            rootcert:
                file: tlsca.cert
            # The server name use to verify the hostname returned by TLS handshake
            serverhostoverride:

    # Peer discovery settings.  Controls how this peer discovers other peers
    discovery:

        # The root nodes are used for bootstrapping purposes, and generally
        # supplied through ENV variables
        rootnode:

        # The duration of time between attempts to asks peers for their connected peers
        period:  5s

        ## leaving this in for example of sub map entry
        # testNodes:
        #    - node   : 1
        #      ip     : 127.0.0.1
        #      port   : 21212
        #    - node   : 2
        #      ip     : 127.0.0.1
        #      port   : 21212

        # Should the discovered nodes and their reputations
        # be stored in DB and persisted between restarts
        persist:    true

        # if peer discovery is off
        # the peer window will show
        # only what retrieved by active
        # peer [true/false]
        enabled:    true

        # number of workers that
        # test the peers for being
        # online [1..10]
        workers: 8

        # the period in seconds with which the discovery
        # tries to reconnect to successful nodes
        # 0 means the nodes are not reconnected
        touchPeriod: 600

        # the maximum nuber of nodes to reconnect to
        # -1 for unlimited
        touchMaxNodes: 100

    # Path on the file system where peer will store data
    fileSystemPath: /tmp/hyperledger/test/tmpdb


    profile:
        enabled:     false
        listenAddress: 0.0.0.0:6060

###############################################################################
#
#    VM section
#
###############################################################################
vm:

    # Endpoint of the vm management system.  For docker can be one of the following in general
    # unix:///var/run/docker.sock
    # http://localhost:2375
    # https://localhost:2376
    endpoint: unix:///var/run/docker.sock

    # settings for docker vms
    docker:
        tls:
            enabled: false
            cert:
                file: /path/to/server.pem
            ca:
                file: /path/to/ca.pem
            key:
                file: /path/to/server-key.pem
        attachStdout: true
###############################################################################
#
#    Chaincode section
#
###############################################################################
chaincode:

    # The id is used by the Chaincode stub to register the executing Chaincode
    # ID with the Peer and is generally supplied through ENV variables
    # the `path` form of ID is provided when installing the chaincode.
    # The `name` is used for all other requests and can be any string.
    id:
        path:
        name:

    # Generic builder image with Go pre-installed.
    # TWO_DIGIT_VERSION represents Fabric major.minor version.
    builder: $(DOCKER_NS)/fabric-ccenv:$(TWO_DIGIT_VERSION)

    # Enables/disables force pulling of the base docker images (listed below)
    # during user chaincode instantiation.
    # Useful when using moving image tags (such as :latest)
    pull: false

    golang:
        # Compiled Go chaincodes will be copied to the fabric-baseos runtime image.
        # TWO_DIGIT_VERSION represents Fabric major.minor version.
        runtime: $(DOCKER_NS)/fabric-baseos:$(TWO_DIGIT_VERSION)

        # whether or not golang chaincode should be linked dynamically
        dynamicLink: false

    java:
        # This is an image based on eclipse temurin with addition compiler
        # tools added for java shim layer packaging.
        # This image is packed with shim layer libraries that are necessary
        # for Java chaincode runtime.
        runtime: $(DOCKER_NS)/fabric-javaenv:2.5

    node:
        # This is an image based on node:$(NODE_VER)-alpine
        runtime: $(DOCKER_NS)/fabric-nodeenv:2.5

    # timeout in millisecs for starting up a container and waiting for Register
    # to come through. 1sec should be plenty for chaincode unit tests
    startuptimeout: 1000

    # timeout in millisecs for invokes and initialize commands
    # this timeout is used by all chaincodes in all the channels including
    # system chaincodes. Default is 30000ms (30 seconds)
    executetimeout: 30000


    #timeout in millisecs for deploying chaincode from a remote repository.
    deploytimeout: 60000

    #mode - options are "dev", "net"
    #dev - in dev mode, user runs the chaincode after starting validator from
    # command line on local machine
    #net - in net mode validator will run chaincode in a docker container

    mode: net
    # typically installpath should not be modified. Otherwise, user must ensure
    # the chaincode executable is placed in the path specified by installpath in
    # the image
    installpath: /opt/gopath/bin/

    #keepalive in seconds. In situations where the communication goes through a
    #proxy that does not support keep-alive, this parameter will maintain connection
    #between peer and chaincode.
    #A value <= 0 turns keepalive off
    keepalive: 1

    # enabled system chaincodes
    system:
        cscc: enable
        lscc: enable
        escc: enable
        vscc: enable

    # Logging section for the chaincode container
    logging:
      # Default level for all loggers within the chaincode container
      level:  info
      # Override default level for the 'shim' logger
      shim:   warning
      # Format for the chaincode container logs
      format: '%{color}%{time:2006-01-02 15:04:05.000 MST} [%{module}] %{shortfunc} -> %{level:.4s} %{id:03x}%{color:reset} %{message}'

###############################################################################
#
#    Ledger section - ledger configuration encompasses both the blockchain
#    and the state
#
###############################################################################
ledger:

  blockchain:

  state:
    # stateDatabase - options are "goleveldb", "CouchDB"
    # goleveldb - default state database stored in goleveldb.
    # CouchDB - store state database in CouchDB
    stateDatabase: goleveldb
    couchDBConfig:
       couchDBAddress: 127.0.0.1:5984
       username:
       password:
       # Number of retries for CouchDB errors
       maxRetries: 3
       # Number of retries for CouchDB errors during peer startup
       maxRetriesOnStartup: 10
       # CouchDB request timeout (unit: duration, e.g. 20s)
       requestTimeout: 35s
       # Limit on the number of records to return per query
       queryLimit: 10000
       # Limit on the number of records per CouchDB bulk update batch
       maxBatchUpdateSize: 500

  history:
    # enableHistoryDatabase - options are true or false
    # Indicates if the history of key updates should be stored in goleveldb
    enableHistoryDatabase: true


################################################################################
#
#   SECTION: STATETRANSFER
#
#   - This applies to recovery behavior when the replica has detected
#     a state transfer is required
#
#   - This might happen:
#     - During a view change in response to a faulty primary
#     - After a network outage which has isolated the replica
#     - If the current blockchain/state is determined to be corrupt
#
################################################################################
statetransfer:

    # Should a replica attempt to fix damaged blocks?
    # In general, this should be set to true, setting to false will cause
    # the replica to panic, and require a human's intervention to intervene
    # and fix the corruption
    recoverdamage: true

    # The number of blocks to retrieve per sync request
    blocksperrequest: 20

    # The maximum number of state deltas to attempt to retrieve
    # If more than this number of deltas is required to play the state up to date
    # then instead the state will be flagged as invalid, and a full copy of the state
    # will be retrieved instead
    maxdeltas: 200

    # Timeouts
    timeout:

        # How long may returning a single block take
        singleblock: 2s

        # How long may returning a single state delta take
        singlestatedelta: 2s

        # How long may transferring the complete state take
        fullstate: 60s
