// Code generated by counterfeiter. DO NOT EDIT.
package fake

import (
	"sync"
	"time"

	"github.com/hyperledger/fabric/common/deliverclient/blocksprovider"
)

type BlockProgressReporter struct {
	BlockProgressStub        func() (uint64, time.Time)
	blockProgressMutex       sync.RWMutex
	blockProgressArgsForCall []struct {
	}
	blockProgressReturns struct {
		result1 uint64
		result2 time.Time
	}
	blockProgressReturnsOnCall map[int]struct {
		result1 uint64
		result2 time.Time
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *BlockProgressReporter) BlockProgress() (uint64, time.Time) {
	fake.blockProgressMutex.Lock()
	ret, specificReturn := fake.blockProgressReturnsOnCall[len(fake.blockProgressArgsForCall)]
	fake.blockProgressArgsForCall = append(fake.blockProgressArgsForCall, struct {
	}{})
	stub := fake.BlockProgressStub
	fakeReturns := fake.blockProgressReturns
	fake.recordInvocation("BlockProgress", []interface{}{})
	fake.blockProgressMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *BlockProgressReporter) BlockProgressCallCount() int {
	fake.blockProgressMutex.RLock()
	defer fake.blockProgressMutex.RUnlock()
	return len(fake.blockProgressArgsForCall)
}

func (fake *BlockProgressReporter) BlockProgressCalls(stub func() (uint64, time.Time)) {
	fake.blockProgressMutex.Lock()
	defer fake.blockProgressMutex.Unlock()
	fake.BlockProgressStub = stub
}

func (fake *BlockProgressReporter) BlockProgressReturns(result1 uint64, result2 time.Time) {
	fake.blockProgressMutex.Lock()
	defer fake.blockProgressMutex.Unlock()
	fake.BlockProgressStub = nil
	fake.blockProgressReturns = struct {
		result1 uint64
		result2 time.Time
	}{result1, result2}
}

func (fake *BlockProgressReporter) BlockProgressReturnsOnCall(i int, result1 uint64, result2 time.Time) {
	fake.blockProgressMutex.Lock()
	defer fake.blockProgressMutex.Unlock()
	fake.BlockProgressStub = nil
	if fake.blockProgressReturnsOnCall == nil {
		fake.blockProgressReturnsOnCall = make(map[int]struct {
			result1 uint64
			result2 time.Time
		})
	}
	fake.blockProgressReturnsOnCall[i] = struct {
		result1 uint64
		result2 time.Time
	}{result1, result2}
}

func (fake *BlockProgressReporter) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.blockProgressMutex.RLock()
	defer fake.blockProgressMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *BlockProgressReporter) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ blocksprovider.BlockProgressReporter = new(BlockProgressReporter)
