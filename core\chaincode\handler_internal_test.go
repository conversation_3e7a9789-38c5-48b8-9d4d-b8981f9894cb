/*
Copyright IBM Corp. All Rights Reserved.

SPDX-License-Identifier: Apache-2.0
*/

package chaincode

import (
	"github.com/hyperledger/fabric/core/container/ccintf"
)

// Helpers to access unexported state.

func SetHandlerChaincodeID(h *Handler, chaincodeID string) {
	h.chaincodeID = chaincodeID
}

func SetHandlerChatStream(h *<PERSON><PERSON>, chatStream ccintf.ChaincodeStream) {
	h.chatStream = chatStream
}

func StreamDone(h *Handler) <-chan struct{} {
	return h.streamDone()
}

func SetStreamDoneChan(h *Handler, ch chan struct{}) {
	h.streamDoneChan = ch
}
