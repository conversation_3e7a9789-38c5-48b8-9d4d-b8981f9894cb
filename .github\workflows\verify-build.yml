# Copyright the Hyperledger Fabric contributors. All rights reserved.
#
# SPDX-License-Identifier: Apache-2.0

name: Verify Build
on:
  push:
    branches: ["**"]
  pull_request:
    branches: ["**"]
  workflow_dispatch:

env:
  GOPATH: /opt/go
  PATH: /opt/go/bin:/bin:/usr/bin:/sbin:/usr/sbin:/usr/local/bin:/usr/local/sbin

permissions:
  contents: read # to fetch code (actions/checkout)

jobs:
  basic-checks:
    name: Basic Checks
    runs-on: ${{ github.repository == 'hyperledger/fabric' && 'fabric-ubuntu-24.04' || 'ubuntu-24.04' }}
    steps:
      - uses: actions/checkout@v4
        name: Checkout Fabric Code
        with:
          fetch-depth: 0
      - uses: actions/setup-go@v5
        name: Install Go
        with:
          go-version-file: go.mod
      - run: make basic-checks
        name: Run Basic Checks
  unit-tests:
    name: Unit Tests
    needs: basic-checks
    runs-on: ${{ github.repository == 'hyperledger/fabric' && 'fabric-ubuntu-24.04' || 'ubuntu-24.04' }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
        name: Checkout Fabric Code
      - uses: actions/setup-go@v5
        name: Install Go
        with:
          go-version-file: go.mod
      - run: ci/scripts/setup_hsm.sh
        name: Install SoftHSM
      - run: make unit-test
        name: Run Unit Tests
  integration-tests:
    name: Integration Tests
    needs: basic-checks
    strategy:
      fail-fast: false
      matrix:
        INTEGRATION_TEST_SUITE:
          - raft
          - pvtdata
          - pvtdatapurge e2e
          - ledger
          - lifecycle
          - smartbft
          - discovery gossip devmode pluggable
          - gateway idemix pkcs11 configtx configtxlator
          - sbe nwo msp
    runs-on: ${{ github.repository == 'hyperledger/fabric' && 'fabric-ubuntu-24.04' || 'ubuntu-24.04' }}
    steps:
      - uses: actions/checkout@v4
        name: Checkout Fabric Code
      - uses: actions/setup-go@v5
        name: Install Go
        with:
          go-version-file: go.mod
      - run: ci/scripts/setup_hsm.sh
        name: Install SoftHSM
      - run: make integration-test INTEGRATION_TEST_SUITE="${{matrix.INTEGRATION_TEST_SUITE}}"
        name: Run Integration Tests
